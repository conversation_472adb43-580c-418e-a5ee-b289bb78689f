{% extends 'base_new.html' %}

{% block title %}Release Management - MSRRMS{% endblock %}

{% block page_title %}Release Management{% endblock %}
{% block mobile_title %}Release Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0" x-data="releaseManagement()">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Release Management</h1>
            <p class="mt-1 text-lg text-gray-600">Manage approved requests ready for release</p>
        </div>

        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Ready for Release</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ total_approved }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <form hx-get="{% url 'supply:release_management' %}" 
                      hx-target="#release-list" 
                      hx-swap="outerHTML"
                      class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    
                    <!-- Department Filter -->
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                        <select name="department" id="department" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">All Departments</option>
                            {% for dept in departments %}
                            <option value="{{ dept }}" {% if dept == department_filter %}selected{% endif %}>{{ dept }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ search_query }}"
                               placeholder="Request ID, item, purpose..."
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>

                    <!-- Sort -->
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700">Sort By</label>
                        <select name="sort" id="sort" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="-approved_at" {% if sort_by == '-approved_at' %}selected{% endif %}>Newest Approved</option>
                            <option value="approved_at" {% if sort_by == 'approved_at' %}selected{% endif %}>Oldest Approved</option>
                            <option value="department" {% if sort_by == 'department' %}selected{% endif %}>Department A-Z</option>
                            <option value="-department" {% if sort_by == '-department' %}selected{% endif %}>Department Z-A</option>
                            <option value="item__name" {% if sort_by == 'item__name' %}selected{% endif %}>Item A-Z</option>
                            <option value="-item__name" {% if sort_by == '-item__name' %}selected{% endif %}>Item Z-A</option>
                        </select>
                    </div>

                    <!-- Filter Button -->
                    <div class="flex items-end">
                        <button type="submit" 
                                class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Batch Operations -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Batch Operations</h3>
                    <div class="flex space-x-3">
                        <button @click="showBulkReleaseModal = true"
                                :disabled="selectedRequests.length === 0"
                                :class="selectedRequests.length === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-purple-600 hover:bg-purple-700'"
                                class="px-4 py-2 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                            Release Selected (<span x-text="selectedRequests.length"></span>)
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests List -->
        <div id="release-list">
            {% include 'supply/gso/release_list.html' %}
        </div>

        <!-- Bulk Release Modal -->
        <div x-show="showBulkReleaseModal" 
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showBulkReleaseModal = false">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <form hx-post="{% url 'supply:batch_operations' %}"
                      hx-target="#release-list"
                      hx-swap="outerHTML">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_release">
                    <template x-for="requestId in selectedRequests">
                        <input type="hidden" name="selected_requests" :value="requestId">
                    </template>
                    
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 text-center">Bulk Release</h3>
                        <div class="mt-4">
                            <label for="release_notes" class="block text-sm font-medium text-gray-700">Release Notes (Optional)</label>
                            <textarea name="release_notes" 
                                      id="release_notes"
                                      rows="3" 
                                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                                      placeholder="Add notes for this bulk release..."></textarea>
                        </div>
                        <div class="flex justify-center space-x-4 px-4 py-3 mt-4">
                            <button type="submit"
                                    @click="showBulkReleaseModal = false; selectedRequests = []"
                                    class="px-4 py-2 bg-purple-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500">
                                Release All
                            </button>
                            <button type="button"
                                    @click="showBulkReleaseModal = false" 
                                    class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function releaseManagement() {
    return {
        selectedRequests: [],
        showBulkReleaseModal: false,
        
        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }
        },
        
        toggleAll() {
            const checkboxes = document.querySelectorAll('input[name="request_checkbox"]');
            if (this.selectedRequests.length === checkboxes.length) {
                this.selectedRequests = [];
            } else {
                this.selectedRequests = Array.from(checkboxes).map(cb => parseInt(cb.value));
            }
        }
    }
}
</script>
{% endblock %}
