<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MSRRMS{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.13/24/outline/index.js"></script>

    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        .htmx-indicator {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        .nav-link-active {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-right: 4px solid #3b82f6;
        }
    </style>

    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-100 font-sans leading-normal tracking-normal">

    <div x-data="{ sidebarOpen: false }">
        <!-- Sidebar -->
        <div class="flex h-screen bg-gray-200">
            <!-- Backdrop -->
            <div :class="sidebarOpen ? 'block' : 'hidden'" @click="sidebarOpen = false" class="fixed inset-0 z-20 transition-opacity bg-black opacity-50 lg:hidden"></div>

            <!-- Sidebar -->
            <div :class="sidebarOpen ? 'translate-x-0 ease-out' : '-translate-x-full ease-in'" class="fixed inset-y-0 left-0 z-30 w-64 overflow-y-auto sidebar-transition bg-white shadow-xl lg:translate-x-0 lg:static lg:inset-0 border-r border-gray-200">
                <!-- Logo Section -->
                <div class="flex items-center justify-center py-6 px-4 bg-gradient-to-r from-blue-600 to-blue-800">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                        <span class="text-white text-xl font-bold">MSRRMS</span>
                    </div>
                </div>

                <!-- User Info Section -->
                {% if user.is_authenticated %}
                <div class="px-4 py-4 bg-gray-50 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <span class="text-blue-600 font-semibold text-sm">
                                {{ user.first_name|first|default:user.username|first|upper }}{{ user.last_name|first|upper }}
                            </span>
                        </div>
                        <div class="ml-3 flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                                {{ user.get_full_name|default:user.username }}
                            </p>
                            <p class="text-xs text-gray-500 truncate">
                                {% if user.userprofile.role == 'GSO' %}
                                    General Supply Officer
                                {% else %}
                                    {{ user.userprofile.department|default:"Department User" }}
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Navigation -->
                <nav class="flex-1 py-4">
                    {% if user.is_authenticated %}
                        {% include 'supply/sidebar.html' %}
                    {% endif %}
                </nav>
            </div>

            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Header -->
                <header class="flex items-center justify-between px-6 py-4 bg-white shadow-sm border-b border-gray-200">
                    <div class="flex items-center space-x-4">
                        <!-- Mobile Menu Button -->
                        <button @click="sidebarOpen = true" class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 lg:hidden transition-colors duration-200">
                            <svg class="h-6 w-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 6H20M4 12H20M4 18H11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </button>

                        <!-- Page Title (visible on mobile) -->
                        <div class="lg:hidden">
                            <h1 class="text-lg font-semibold text-gray-900">
                                {% block mobile_title %}MSRRMS{% endblock %}
                            </h1>
                        </div>
                    </div>

                    <!-- Right Side -->
                    <div class="flex items-center space-x-4">
                        {% if user.is_authenticated %}
                            <!-- Notifications (placeholder) -->
                            <button class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25V12l2.25-2.25V9.75a6 6 0 0 1 6-6z" />
                                </svg>
                            </button>

                            <!-- User Dropdown -->
                            <div x-data="{ dropdownOpen: false }" class="relative">
                                <button @click="dropdownOpen = !dropdownOpen" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-blue-600 font-semibold text-sm">
                                            {{ user.first_name|first|default:user.username|first|upper }}{{ user.last_name|first|upper }}
                                        </span>
                                    </div>
                                    <div class="hidden md:block text-left">
                                        <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                                        <p class="text-xs text-gray-500">
                                            {% if user.userprofile.role == 'GSO' %}GSO{% else %}{{ user.userprofile.department|default:"User" }}{% endif %}
                                        </p>
                                    </div>
                                    <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- Dropdown Backdrop -->
                                <div x-show="dropdownOpen" @click="dropdownOpen = false" class="fixed inset-0 h-full w-full z-10"></div>

                                <!-- Dropdown Menu -->
                                <div x-show="dropdownOpen"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
                                    <div class="py-2">
                                        <div class="px-4 py-2 border-b border-gray-100">
                                            <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                                            <p class="text-xs text-gray-500">{{ user.email }}</p>
                                        </div>
                                        <a href="{% url 'supply:profile' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200">
                                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                            Your Profile
                                        </a>
                                        <a href="{% url 'logout' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
                                            <svg class="h-4 w-4 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                            </svg>
                                            Logout
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </header>

                <!-- Main Content -->
                <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
                    <div class="container mx-auto px-6 py-8">
                        <!-- Page Header -->
                        <div class="hidden lg:block mb-6">
                            <h1 class="text-2xl font-bold text-gray-900">
                                {% block page_title %}Dashboard{% endblock %}
                            </h1>
                            {% block page_description %}
                            <p class="text-gray-600 mt-1">
                                {% if user.userprofile.role == 'GSO' %}
                                    Manage supply requests and inventory
                                {% else %}
                                    View your requests and create new ones
                                {% endif %}
                            </p>
                            {% endblock %}
                        </div>

                        <!-- Messages -->
                        {% if messages %}
                            <div class="mb-6 space-y-2">
                                {% for message in messages %}
                                    <div class="flex items-center p-4 rounded-lg border-l-4
                                               {% if message.tags == 'success' %}bg-green-50 border-green-400 text-green-700
                                               {% elif message.tags == 'error' %}bg-red-50 border-red-400 text-red-700
                                               {% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-400 text-yellow-700
                                               {% else %}bg-blue-50 border-blue-400 text-blue-700{% endif %}">
                                        <div class="flex-shrink-0">
                                            {% if message.tags == 'success' %}
                                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            {% elif message.tags == 'error' %}
                                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            {% elif message.tags == 'warning' %}
                                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                </svg>
                                            {% else %}
                                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            {% endif %}
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium">{{ message }}</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Content -->
                        {% block content %}
                        {% endblock %}
                    </div>
                </main>
            </div>
        </div>
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
