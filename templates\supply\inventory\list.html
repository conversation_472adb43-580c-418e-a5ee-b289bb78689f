{% extends 'base_new.html' %}

{% block title %}Inventory Management - MSRRMS{% endblock %}

{% block page_title %}Inventory Management{% endblock %}
{% block mobile_title %}Inventory{% endblock %}

{% block content %}
<div x-data="inventoryManagement()" class="space-y-6">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold">Inventory Management</h1>
                <p class="text-blue-100 mt-1">Manage supply items organized by categories</p>
            </div>
            <div class="hidden md:flex items-center space-x-6">
                <div class="text-center">
                    <div class="text-2xl font-bold">{{ total_items }}</div>
                    <div class="text-blue-200 text-sm">Total Items</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-200">{{ low_stock_count }}</div>
                    <div class="text-blue-200 text-sm">Low Stock</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-300">{{ out_of_stock_count }}</div>
                    <div class="text-blue-200 text-sm">Out of Stock</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alert -->
    {% if low_stock_count > 0 %}
    <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm">
                    <strong>{{ low_stock_count }} item{{ low_stock_count|pluralize }} running low on stock!</strong>
                    <a href="{% url 'supply:low_stock_alerts' %}" class="underline ml-2">View Details</a>
                </p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Search and Filter -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
        <form method="get" class="flex flex-wrap gap-4 items-end">
            <div class="flex-1 min-w-64">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Items</label>
                <input type="text" 
                       id="search" 
                       name="search" 
                       value="{{ search_query }}"
                       placeholder="Search by name or description..."
                       class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            <div class="flex items-center">
                <input type="checkbox" 
                       id="low_stock" 
                       name="low_stock" 
                       value="1"
                       {% if show_low_stock %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="low_stock" class="ml-2 block text-sm text-gray-900">
                    Show only low stock items
                </label>
            </div>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Filter
            </button>
            {% if search_query or show_low_stock %}
            <a href="{% url 'supply:inventory' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Clear
            </a>
            {% endif %}
        </form>
    </div>

    <!-- Inventory Items -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        {% if page_obj %}
        <ul class="divide-y divide-gray-200">
            {% for item in page_obj %}
            <li>
                <div class="px-4 py-4 flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            {% if item.is_low_stock %}
                            <div class="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            {% else %}
                            <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                </svg>
                            </div>
                            {% endif %}
                        </div>
                        <div class="ml-4">
                            <div class="flex items-center">
                                <div class="text-sm font-medium text-gray-900">
                                    <a href="{% url 'supply:inventory_detail' item.id %}" class="hover:text-blue-600">
                                        {{ item.name }}
                                    </a>
                                </div>
                                {% if item.is_low_stock %}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Low Stock
                                </span>
                                {% endif %}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ item.description|default:"No description" }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">
                                {{ item.current_stock }} {{ item.unit }}
                            </div>
                            <div class="text-sm text-gray-500">
                                Min: {{ item.minimum_stock }} {{ item.unit }}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <a href="{% url 'supply:inventory_detail' item.id %}" 
                               class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                View
                            </a>
                            <a href="{% url 'supply:inventory_edit' item.id %}" 
                               class="text-green-600 hover:text-green-900 text-sm font-medium">
                                Edit
                            </a>
                            <a href="{% url 'supply:inventory_adjust' item.id %}" 
                               class="text-yellow-600 hover:text-yellow-900 text-sm font-medium">
                                Adjust
                            </a>
                        </div>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if show_low_stock %}&low_stock=1{% endif %}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if show_low_stock %}&low_stock=1{% endif %}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium">{{ page_obj.paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if show_low_stock %}&low_stock=1{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if show_low_stock %}&low_stock=1{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if show_low_stock %}&low_stock=1{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
        {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No inventory items</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by adding a new supply item.</p>
            <div class="mt-6">
                <a href="{% url 'supply:inventory_add' %}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Add New Item
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}