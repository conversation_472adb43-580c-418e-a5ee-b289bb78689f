{% extends 'base.html' %}

{% block title %}Request History - MSRRMS{% endblock %}

{% block content %}
<div class="px-4 sm:px-0">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        {% if user.userprofile.role == 'GSO' %}
                            All Supply Requests
                        {% else %}
                            My Supply Requests
                        {% endif %}
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">
                        {% if user.userprofile.role == 'GSO' %}
                            View and manage all supply requests from departments
                        {% else %}
                            Track the status of your submitted supply requests
                        {% endif %}
                    </p>
                </div>
                {% if user.userprofile.role != 'GSO' %}
                <div>
                    <a href="{% url 'supply:request_create' %}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        New Request
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <form method="get" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                    <div class="flex-1">
                        <label for="{{ filter_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Search
                        </label>
                        {{ filter_form.search }}
                    </div>
                    <div class="sm:w-48">
                        <label for="{{ filter_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Status
                        </label>
                        {{ filter_form.status }}
                    </div>
                    <div>
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Requests List -->
        <div id="requests-list">
            {% include 'supply/requests/history_list.html' %}
        </div>
    </div>
</div>

<!-- Auto-refresh for real-time updates -->
<div hx-get="{% url 'supply:request_status_update' %}?request_ids={% for request in page_obj %}{{ request.id }}{% if not forloop.last %},{% endif %}{% endfor %}" 
     hx-trigger="every 30s" 
     hx-target="#requests-list" 
     hx-swap="outerHTML">
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set up HTMX attributes for filter form
    const searchInput = document.getElementById('{{ filter_form.search.id_for_label }}');
    const statusSelect = document.getElementById('{{ filter_form.status.id_for_label }}');
    
    if (searchInput) {
        searchInput.setAttribute('hx-get', '{% url "supply:request_history" %}');
        searchInput.setAttribute('hx-target', '#requests-list');
        searchInput.setAttribute('hx-trigger', 'keyup changed delay:500ms');
        searchInput.setAttribute('hx-include', '[name="status"]');
    }
    
    if (statusSelect) {
        statusSelect.setAttribute('hx-get', '{% url "supply:request_history" %}');
        statusSelect.setAttribute('hx-target', '#requests-list');
        statusSelect.setAttribute('hx-trigger', 'change');
        statusSelect.setAttribute('hx-include', '[name="search"]');
    }
});
</script>
{% endblock %}