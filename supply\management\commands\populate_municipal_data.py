from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from supply.models import UserProfile, SupplyItem, SupplyRequest
import random


class Command(BaseCommand):
    help = 'Populate municipal departments and supply inventory data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--delete-gso',
            action='store_true',
            help='Delete existing GSO accounts',
        )

    def handle(self, *args, **options):
        # Delete existing GSO accounts if requested
        if options['delete_gso']:
            self.stdout.write('Deleting existing GSO accounts...')
            gso_users = User.objects.filter(userprofile__role='GSO')
            count = gso_users.count()
            gso_users.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {count} GSO accounts')
            )

        # Create municipal departments
        self.stdout.write('Creating municipal departments...')
        self.create_departments()

        # Create supply inventory
        self.stdout.write('Creating supply inventory...')
        self.create_supply_inventory()

        # Create new GSO account
        self.stdout.write('Creating new GSO account...')
        self.create_gso_account()

        self.stdout.write(
            self.style.SUCCESS('Successfully populated municipal data!')
        )

    def create_departments(self):
        """Create department users for all municipal offices"""
        departments = [
            ("Mayor's Office", "mayor", "Municipal", "Mayor"),
            ("Vice Mayor's Office", "vicemayor", "Vice", "Mayor"),
            ("Municipal Administrator's Office", "admin_office", "Municipal", "Administrator"),
            ("Municipal Planning and Development Office", "mpdo", "Planning", "Officer"),
            ("Municipal Civil Registrar's Office", "civilreg", "Civil", "Registrar"),
            ("Municipal Treasurer's Office", "treasurer", "Municipal", "Treasurer"),
            ("Municipal Accountant's Office", "accountant", "Municipal", "Accountant"),
            ("Municipal Budget Office", "budget", "Budget", "Officer"),
            ("Municipal Assessor's Office", "assessor", "Municipal", "Assessor"),
            ("Municipal Engineering Office", "meo", "Municipal", "Engineer"),
            ("Municipal Health Office", "mho", "Health", "Officer"),
            ("Municipal Social Welfare and Development Office", "mswdo", "Social", "Worker"),
            ("Municipal Environment and Natural Resources Office", "menro", "Environment", "Officer"),
            ("Office of the Municipal Agriculturist", "oma", "Municipal", "Agriculturist"),
            ("Business Permit & Licensing Office", "bplo", "Business", "Officer"),
            ("Human Resource Management Office", "hrmo", "HR", "Manager"),
            ("Public Employment Service Office", "peso", "Employment", "Officer"),
            ("Municipal Disaster Risk Reduction and Management Office", "mdrrmo", "Disaster", "Officer"),
            ("Municipal Economic Enterprise Development Office", "meedo", "Economic", "Officer"),
        ]

        for dept_name, username, first_name, last_name in departments:
            try:
                # Check if user already exists
                user, created = User.objects.get_or_create(
                    username=username,
                    defaults={
                        'email': f'{username}@municipality.gov.ph',
                        'first_name': first_name,
                        'last_name': last_name
                    }
                )

                if created:
                    user.set_password('municipal123')
                    user.save()
                    self.stdout.write(f'Created user: {username}')
                else:
                    self.stdout.write(f'User {username} already exists')

                # Check if profile exists
                profile, profile_created = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'role': 'DEPARTMENT',
                        'department': dept_name,
                        'phone': f'09{random.randint(100000000, 999999999)}'
                    }
                )

                if profile_created:
                    self.stdout.write(f'Created profile for {username} - {dept_name}')
                else:
                    # Update existing profile
                    profile.department = dept_name
                    profile.role = 'DEPARTMENT'
                    profile.save()
                    self.stdout.write(f'Updated profile for {username} - {dept_name}')

            except Exception as e:
                self.stdout.write(f'Error processing user {username}: {str(e)}')

    def create_supply_inventory(self):
        """Create comprehensive supply inventory"""
        supply_categories = {
            'Office Supplies': [
                ('Bond Paper A4', 'ream', 'White bond paper 80gsm'),
                ('Bond Paper Legal', 'ream', 'White bond paper legal size'),
                ('Ballpoint Pen Blue', 'piece', 'Blue ink ballpoint pen'),
                ('Ballpoint Pen Black', 'piece', 'Black ink ballpoint pen'),
                ('Ballpoint Pen Red', 'piece', 'Red ink ballpoint pen'),
                ('Pencil No. 2', 'piece', 'Standard No. 2 pencil'),
                ('Eraser', 'piece', 'White rubber eraser'),
                ('Ruler 12 inch', 'piece', 'Plastic ruler 12 inches'),
                ('Stapler', 'piece', 'Standard office stapler'),
                ('Staple Wire', 'box', 'Staple wire No. 35'),
                ('Paper Clips', 'box', 'Standard paper clips'),
                ('Rubber Bands', 'pack', 'Assorted rubber bands'),
                ('Manila Folder', 'piece', 'Legal size manila folder'),
                ('Expanding Folder', 'piece', 'Legal size expanding folder'),
                ('Fastener', 'box', 'Metal fasteners'),
                ('Scotch Tape', 'piece', 'Transparent tape 1 inch'),
                ('Masking Tape', 'piece', 'Masking tape 1 inch'),
                ('Glue Stick', 'piece', 'Solid glue stick'),
                ('Correction Fluid', 'piece', 'White correction fluid'),
                ('Marker Permanent Black', 'piece', 'Black permanent marker'),
                ('Marker Permanent Red', 'piece', 'Red permanent marker'),
                ('Highlighter Yellow', 'piece', 'Yellow highlighter'),
                ('Calculator', 'piece', 'Basic calculator'),
                ('Clipboard', 'piece', 'Letter size clipboard'),
                ('Binder Clips Large', 'box', 'Large binder clips'),
                ('Binder Clips Small', 'box', 'Small binder clips'),
            ],
            'Computer Supplies': [
                ('Printer Ink Black', 'cartridge', 'Black ink cartridge'),
                ('Printer Ink Color', 'cartridge', 'Color ink cartridge'),
                ('Toner Cartridge', 'piece', 'Laser printer toner'),
                ('CD-R', 'piece', 'Blank CD-R disc'),
                ('DVD-R', 'piece', 'Blank DVD-R disc'),
                ('USB Flash Drive 16GB', 'piece', '16GB USB flash drive'),
                ('USB Flash Drive 32GB', 'piece', '32GB USB flash drive'),
                ('Mouse Pad', 'piece', 'Computer mouse pad'),
                ('Keyboard Cover', 'piece', 'Keyboard dust cover'),
                ('Monitor Screen Cleaner', 'bottle', 'LCD screen cleaner'),
            ],
            'Cleaning Supplies': [
                ('Toilet Paper', 'roll', 'Toilet tissue paper'),
                ('Hand Soap', 'bottle', 'Liquid hand soap'),
                ('Dishwashing Liquid', 'bottle', 'Dishwashing detergent'),
                ('Floor Wax', 'gallon', 'Floor wax polish'),
                ('Disinfectant', 'bottle', 'Multi-surface disinfectant'),
                ('Bleach', 'bottle', 'Chlorine bleach'),
                ('Trash Bags Large', 'pack', 'Large garbage bags'),
                ('Trash Bags Small', 'pack', 'Small garbage bags'),
                ('Broom', 'piece', 'Soft broom'),
                ('Mop', 'piece', 'String mop'),
                ('Dustpan', 'piece', 'Plastic dustpan'),
                ('Rubber Gloves', 'pair', 'Cleaning rubber gloves'),
                ('Sponge', 'piece', 'Kitchen sponge'),
                ('Scrub Brush', 'piece', 'Cleaning scrub brush'),
                ('Air Freshener', 'bottle', 'Room air freshener'),
            ],
            'Medical Supplies': [
                ('First Aid Kit', 'set', 'Complete first aid kit'),
                ('Bandages', 'box', 'Adhesive bandages'),
                ('Gauze Pads', 'pack', 'Sterile gauze pads'),
                ('Medical Tape', 'roll', 'Medical adhesive tape'),
                ('Antiseptic', 'bottle', 'Antiseptic solution'),
                ('Thermometer', 'piece', 'Digital thermometer'),
                ('Disposable Gloves', 'box', 'Latex disposable gloves'),
                ('Face Masks', 'box', 'Surgical face masks'),
                ('Hand Sanitizer', 'bottle', 'Alcohol-based sanitizer'),
            ],
            'Maintenance Supplies': [
                ('Light Bulb LED 9W', 'piece', '9W LED light bulb'),
                ('Light Bulb Fluorescent 40W', 'piece', '40W fluorescent tube'),
                ('Extension Cord', 'piece', '10-meter extension cord'),
                ('Electrical Tape', 'roll', 'Insulating electrical tape'),
                ('Screwdriver Set', 'set', 'Phillips and flathead set'),
                ('Hammer', 'piece', 'Claw hammer'),
                ('Pliers', 'piece', 'Standard pliers'),
                ('Wrench Set', 'set', 'Adjustable wrench set'),
                ('Nails', 'pack', 'Assorted nails'),
                ('Screws', 'pack', 'Assorted screws'),
                ('Lubricating Oil', 'bottle', 'Multi-purpose oil'),
                ('Paint Brush', 'piece', '2-inch paint brush'),
                ('Sandpaper', 'sheet', 'Medium grit sandpaper'),
            ],
            'Furniture & Equipment': [
                ('Office Chair', 'piece', 'Ergonomic office chair'),
                ('Office Table', 'piece', 'Standard office desk'),
                ('Filing Cabinet', 'piece', '4-drawer filing cabinet'),
                ('Bookshelf', 'piece', '5-tier bookshelf'),
                ('Whiteboard', 'piece', '4x6 feet whiteboard'),
                ('Cork Board', 'piece', '3x4 feet cork board'),
                ('Electric Fan', 'piece', 'Ceiling electric fan'),
                ('Water Dispenser', 'piece', 'Hot and cold dispenser'),
                ('Microwave Oven', 'piece', 'Countertop microwave'),
                ('Refrigerator', 'piece', 'Small office refrigerator'),
            ]
        }

        for category, items in supply_categories.items():
            for name, unit, description in items:
                # Check if item already exists
                if SupplyItem.objects.filter(name=name).exists():
                    continue

                SupplyItem.objects.create(
                    name=name,
                    description=description,
                    unit=unit,
                    current_stock=random.randint(10, 100),
                    minimum_stock=random.randint(5, 20)
                )

        self.stdout.write(f'Created supply inventory items')

    def create_gso_account(self):
        """Create new GSO account"""
        try:
            # Create or get GSO user
            gso_user, created = User.objects.get_or_create(
                username='gso_admin',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'GSO',
                    'last_name': 'Administrator'
                }
            )

            if created:
                gso_user.set_password('gso123')
                gso_user.save()
                self.stdout.write('Created GSO admin user')
            else:
                self.stdout.write('GSO admin user already exists')

            # Create or get GSO profile
            profile, profile_created = UserProfile.objects.get_or_create(
                user=gso_user,
                defaults={
                    'role': 'GSO',
                    'department': 'General Services Office',
                    'phone': '***********'
                }
            )

            if profile_created:
                self.stdout.write('Created GSO admin profile')
            else:
                # Update existing profile
                profile.role = 'GSO'
                profile.department = 'General Services Office'
                profile.save()
                self.stdout.write('Updated GSO admin profile')

            self.stdout.write('GSO Account Details:')
            self.stdout.write('Username: gso_admin')
            self.stdout.write('Password: gso123')
        except Exception as e:
            self.stdout.write(f'Error creating GSO account: {str(e)}')
