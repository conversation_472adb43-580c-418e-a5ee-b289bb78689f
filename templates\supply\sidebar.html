{% if user.userprofile and user.userprofile.role == 'GSO' %}
    <!-- GSO Navigation -->
    <div class="px-4 space-y-2">
        <!-- Section Header -->
        <div class="px-3 py-2">
            <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">GSO Management</h3>
        </div>

        <!-- Dashboard -->
        <a href="{% url 'supply:gso_dashboard' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'gso_dashboard' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                </svg>
            </div>
            <span class="flex-1">Dashboard</span>
            {% if request.resolver_match.url_name == 'gso_dashboard' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>

        <!-- Release Management -->
        <a href="{% url 'supply:release_management' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'release_management' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <span class="flex-1">Release Management</span>
            {% if request.resolver_match.url_name == 'release_management' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>

        <!-- Inventory Management -->
        <a href="{% url 'supply:inventory' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'inventory' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
            </div>
            <span class="flex-1">Inventory</span>
            {% if request.resolver_match.url_name == 'inventory' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>

        <!-- Reports -->
        <a href="{% url 'supply:reports' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'reports' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V7a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <span class="flex-1">Reports</span>
            {% if request.resolver_match.url_name == 'reports' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>
    </div>
{% else %}
    <!-- Department User Navigation -->
    <div class="px-4 space-y-2">
        <!-- Section Header -->
        <div class="px-3 py-2">
            <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Department Portal</h3>
        </div>

        <!-- Dashboard -->
        <a href="{% url 'supply:dashboard' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'dashboard' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                </svg>
            </div>
            <span class="flex-1">Dashboard</span>
            {% if request.resolver_match.url_name == 'dashboard' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>

        <!-- New Request -->
        <a href="{% url 'supply:request_create' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'request_create' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <span class="flex-1">New Request</span>
            {% if request.resolver_match.url_name == 'request_create' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>

        <!-- My Requests -->
        <a href="{% url 'supply:request_history' %}"
           class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
                  {% if request.resolver_match.url_name == 'request_history' %}
                      bg-blue-100 text-blue-700 border-r-4 border-blue-500
                  {% else %}
                      text-gray-700 hover:bg-blue-50 hover:text-blue-600
                  {% endif %}">
            <div class="flex-shrink-0 w-6 h-6 mr-3">
                <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <span class="flex-1">My Requests</span>
            {% if request.resolver_match.url_name == 'request_history' %}
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            {% endif %}
        </a>
    </div>
{% endif %}

<!-- User Profile Section -->
<div class="px-4 mt-8 pt-6 border-t border-gray-200">
    <div class="px-3 py-2">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Account</h3>
    </div>

    <!-- Profile -->
    <a href="{% url 'supply:profile' %}"
       class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out
              {% if request.resolver_match.url_name == 'profile' %}
                  bg-blue-100 text-blue-700 border-r-4 border-blue-500
              {% else %}
                  text-gray-700 hover:bg-blue-50 hover:text-blue-600
              {% endif %}">
        <div class="flex-shrink-0 w-6 h-6 mr-3">
            <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
        </div>
        <span class="flex-1">Profile</span>
        {% if request.resolver_match.url_name == 'profile' %}
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
        {% endif %}
    </a>

    <!-- Logout -->
    <a href="{% url 'logout' %}"
       class="group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out text-gray-700 hover:bg-red-50 hover:text-red-600">
        <div class="flex-shrink-0 w-6 h-6 mr-3">
            <svg class="w-6 h-6 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
        </div>
        <span class="flex-1">Logout</span>
    </a>
</div>
