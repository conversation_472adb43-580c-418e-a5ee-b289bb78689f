{% extends 'base_new.html' %}

{% block title %}Reports Dashboard - MSRRMS{% endblock %}

{% block content %}
<h3 class="text-gray-700 text-3xl font-medium">Reports Dashboard</h3>

<!-- Date Range Filter -->
<div class="mt-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Report Period</h3>
        <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
            <div>
                <label for="start_date" class="text-gray-700">Start Date</label>
                <input type="date" name="start_date" id="start_date" value="{{ start_date|date:'Y-m-d' }}"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div>
                <label for="end_date" class="text-gray-700">End Date</label>
                <input type="date" name="end_date" id="end_date" value="{{ end_date|date:'Y-m-d' }}"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            </div>
            <div>
                <button type="submit" 
                        class="w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                    Update Period
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Summary Statistics -->
<div class="mt-8 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-6">
    <!-- Total Requests -->
    <div class="flex items-center p-4 bg-white rounded-lg shadow-md">
        <div class="p-3 mr-4 text-blue-500 bg-blue-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </div>
        <div>
            <p class="mb-2 text-sm font-medium text-gray-600">Total Requests</p>
            <p class="text-2xl font-semibold text-gray-700">{{ total_requests }}</p>
        </div>
    </div>
    <!-- Pending Requests -->
    <div class="flex items-center p-4 bg-white rounded-lg shadow-md">
        <div class="p-3 mr-4 text-yellow-500 bg-yellow-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div>
            <p class="mb-2 text-sm font-medium text-gray-600">Pending</p>
            <p class="text-2xl font-semibold text-gray-700">{{ pending_requests }}</p>
        </div>
    </div>
    <!-- Approved Requests -->
    <div class="flex items-center p-4 bg-white rounded-lg shadow-md">
        <div class="p-3 mr-4 text-green-500 bg-green-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div>
            <p class="mb-2 text-sm font-medium text-gray-600">Approved</p>
            <p class="text-2xl font-semibold text-gray-700">{{ approved_requests }}</p>
        </div>
    </div>
    <!-- Released Requests -->
    <div class="flex items-center p-4 bg-white rounded-lg shadow-md">
        <div class="p-3 mr-4 text-indigo-500 bg-indigo-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
            </svg>
        </div>
        <div>
            <p class="mb-2 text-sm font-medium text-gray-600">Released</p>
            <p class="text-2xl font-semibold text-gray-700">{{ released_requests }}</p>
        </div>
    </div>
    <!-- Rejected Requests -->
    <div class="flex items-center p-4 bg-white rounded-lg shadow-md">
        <div class="p-3 mr-4 text-red-500 bg-red-100 rounded-full">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path>
            </svg>
        </div>
        <div>
            <p class="mb-2 text-sm font-medium text-gray-600">Rejected</p>
            <p class="text-2xl font-semibold text-gray-700">{{ rejected_requests }}</p>
        </div>
    </div>
</div>

<!-- Report Types -->
<div class="mt-8">
    <h4 class="text-gray-600 text-lg font-medium">Available Reports</h4>
    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <!-- Requests Report -->
        <div class="p-6 bg-white rounded-lg shadow-md">
            <div class="flex items-start">
                <div class="p-3 mr-4 text-blue-500 bg-blue-100 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V7a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-700">Requests Report</h3>
                    <p class="mt-1 text-sm text-gray-500">Detailed breakdown of all supply requests.</p>
                </div>
            </div>
            <div class="mt-4 text-right">
                <a href="{% url 'supply:requests_report' %}?start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}" 
                   class="px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700">
                    View Report
                </a>
            </div>
        </div>
        <!-- Departmental Usage -->
        <div class="p-6 bg-white rounded-lg shadow-md">
            <div class="flex items-start">
                <div class="p-3 mr-4 text-green-500 bg-green-100 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-700">Departmental Usage</h3>
                    <p class="mt-1 text-sm text-gray-500">Usage statistics per department.</p>
                </div>
            </div>
            <div class="mt-4 text-right">
                <a href="{% url 'supply:departmental_usage_report' %}?start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}" 
                   class="px-4 py-2 text-sm text-white bg-green-600 rounded-md hover:bg-green-700">
                    View Report
                </a>
            </div>
        </div>
        <!-- Inventory Report -->
        <div class="p-6 bg-white rounded-lg shadow-md">
            <div class="flex items-start">
                <div class="p-3 mr-4 text-purple-500 bg-purple-100 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path></svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-700">Inventory Status</h3>
                    <p class="mt-1 text-sm text-gray-500">Current stock levels and item details.</p>
                </div>
            </div>
            <div class="mt-4 text-right">
                <a href="{% url 'supply:inventory_report' %}" 
                   class="px-4 py-2 text-sm text-white bg-purple-600 rounded-md hover:bg-purple-700">
                    View Report
                </a>
            </div>
        </div>
        <!-- Analytics -->
        <div class="p-6 bg-white rounded-lg shadow-md">
            <div class="flex items-start">
                <div class="p-3 mr-4 text-orange-500 bg-orange-100 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path></svg>
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-700">Analytics</h3>
                    <p class="mt-1 text-sm text-gray-500">Advanced analytics and trends.</p>
                </div>
            </div>
            <div class="mt-4 text-right">
                <span class="px-4 py-2 text-sm text-gray-500 bg-gray-200 rounded-md cursor-not-allowed">
                    Coming Soon
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Data Tables -->
{% if dept_stats or item_stats %}
<div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Top Departments -->
    {% if dept_stats %}
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Top Departments by Requests</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Requests</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for dept in dept_stats %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ dept.department }}</div>
                            <div class="text-xs text-gray-500">{{ dept.released_requests }} released, {{ dept.approved_requests }} approved</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">{{ dept.total_requests }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- Most Requested Items -->
    {% if item_stats %}
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Most Requested Items</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Requests</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in item_stats %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ item.item__name }}</div>
                            <div class="text-xs text-gray-500">{{ item.total_quantity|default:0 }} {{ item.item__unit }} released</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">{{ item.total_requests }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

{% endblock %}
