from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
import uuid


class UserProfile(models.Model):
    """Extended user profile with role and department information"""
    ROLE_CHOICES = [
        ('DEPARTMENT', 'Department Staff'),
        ('GSO', 'GSO Staff')
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100)
    phone = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"
    
    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"


class SupplyItem(models.Model):
    """Supply items available in inventory"""
    name = models.Cha<PERSON><PERSON><PERSON>(max_length=200)
    description = models.TextField(blank=True)
    unit = models.CharField(max_length=50, help_text="e.g., pieces, boxes, reams, etc.")
    current_stock = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    minimum_stock = models.IntegerField(default=10, validators=[MinValueValidator(0)])
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.current_stock} {self.unit})"
    
    @property
    def is_low_stock(self):
        """Check if item is below minimum stock level"""
        return self.current_stock <= self.minimum_stock
    
    def can_fulfill_quantity(self, quantity):
        """Check if there's enough stock to fulfill a request"""
        return self.current_stock >= quantity
    
    class Meta:
        verbose_name = "Supply Item"
        verbose_name_plural = "Supply Items"
        ordering = ['name']


class SupplyRequest(models.Model):
    """Supply requests submitted by departments - can contain multiple items"""
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('RELEASED', 'Released')
    ]

    request_id = models.CharField(max_length=20, unique=True, editable=False)
    requester = models.ForeignKey(User, on_delete=models.CASCADE, related_name='supply_requests')
    department = models.CharField(max_length=100)
    purpose = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    is_batch_request = models.BooleanField(default=False)

    # Legacy fields for backward compatibility (single item requests)
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, null=True, blank=True)
    quantity = models.IntegerField(validators=[MinValueValidator(1)], null=True, blank=True)
    
    # Approval fields
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='approved_requests'
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    approval_remarks = models.TextField(blank=True)
    
    # Release fields
    released_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='released_requests'
    )
    released_at = models.DateTimeField(null=True, blank=True)
    release_remarks = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def save(self, *args, **kwargs):
        if not self.request_id:
            # Generate unique request ID
            self.request_id = f"REQ-{timezone.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Set department from requester's profile if not set
        if not self.department and hasattr(self.requester, 'userprofile'):
            self.department = self.requester.userprofile.department
            
        super().save(*args, **kwargs)
    
    def approve(self, approved_by, remarks=""):
        """Approve the request"""
        if self.status == 'PENDING' and self.item.can_fulfill_quantity(self.quantity):
            self.status = 'APPROVED'
            self.approved_by = approved_by
            self.approved_at = timezone.now()
            self.approval_remarks = remarks
            self.save()
            return True
        return False
    
    def reject(self, rejected_by, remarks=""):
        """Reject the request"""
        if self.status == 'PENDING':
            self.status = 'REJECTED'
            self.approved_by = rejected_by
            self.approved_at = timezone.now()
            self.approval_remarks = remarks
            self.save()
            return True
        return False
    
    def release(self, released_by, remarks=""):
        """Release the approved request and update inventory"""
        if self.status == 'APPROVED' and self.item.can_fulfill_quantity(self.quantity):
            # Update inventory
            self.item.current_stock -= self.quantity
            self.item.save()
            
            # Update request status
            self.status = 'RELEASED'
            self.released_by = released_by
            self.released_at = timezone.now()
            self.release_remarks = remarks
            self.save()
            
            # Create inventory transaction
            InventoryTransaction.objects.create(
                item=self.item,
                transaction_type='OUT',
                quantity=self.quantity,
                reference_request=self,
                performed_by=released_by,
                remarks=f"Released for request {self.request_id}"
            )
            return True
        return False
    
    @property
    def total_items(self):
        """Get total number of items in this request"""
        if self.is_batch_request:
            return self.request_items.count()
        else:
            return 1 if self.item else 0

    @property
    def items_summary(self):
        """Get a summary of all items in this request"""
        if self.is_batch_request:
            items = self.request_items.all()
            if items.count() <= 3:
                return ", ".join([f"{item.item.name} ({item.quantity})" for item in items])
            else:
                first_three = items[:3]
                summary = ", ".join([f"{item.item.name} ({item.quantity})" for item in first_three])
                return f"{summary} and {items.count() - 3} more items"
        else:
            return f"{self.item.name} ({self.quantity})" if self.item else "No items"

    def __str__(self):
        if self.is_batch_request:
            return f"{self.request_id} - Batch Request ({self.total_items} items)"
        else:
            return f"{self.request_id} - {self.item.name} ({self.quantity})" if self.item else f"{self.request_id} - No items"

    class Meta:
        verbose_name = "Supply Request"
        verbose_name_plural = "Supply Requests"
        ordering = ['-created_at']


class SupplyRequestItem(models.Model):
    """Individual items within a supply request"""
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, related_name='request_items')
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])

    # Item-specific approval/release tracking
    approved_quantity = models.IntegerField(null=True, blank=True, validators=[MinValueValidator(0)])
    released_quantity = models.IntegerField(null=True, blank=True, validators=[MinValueValidator(0)])
    remarks = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.request.request_id} - {self.item.name} ({self.quantity})"

    class Meta:
        verbose_name = "Supply Request Item"
        verbose_name_plural = "Supply Request Items"
        unique_together = ['request', 'item']  # Prevent duplicate items in same request


class InventoryTransaction(models.Model):
    """Track all inventory movements"""
    TRANSACTION_TYPES = [
        ('IN', 'Stock In'),
        ('OUT', 'Stock Out'),
        ('ADJUSTMENT', 'Adjustment')
    ]
    
    item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField()
    reference_request = models.ForeignKey(
        SupplyRequest, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='inventory_transactions'
    )
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    remarks = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.item.name} ({self.quantity})"
    
    class Meta:
        verbose_name = "Inventory Transaction"
        verbose_name_plural = "Inventory Transactions"
        ordering = ['-created_at']


class AuditLog(models.Model):
    """Model to track all system actions for audit purposes"""
    ACTION_TYPES = [
        ('CREATE', 'Create'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
        ('APPROVE', 'Approve'),
        ('REJECT', 'Reject'),
        ('RELEASE', 'Release'),
        ('LOGIN', 'Login'),
        ('LOGOUT', 'Logout'),
        ('INVENTORY_ADJUST', 'Inventory Adjustment'),
        ('BULK_OPERATION', 'Bulk Operation'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    object_type = models.CharField(max_length=50)  # e.g., 'SupplyRequest', 'SupplyItem'
    object_id = models.CharField(max_length=50, null=True, blank=True)
    object_repr = models.CharField(max_length=200)  # String representation of the object
    changes = models.TextField(blank=True, default='{}')  # Store field changes as JSON string
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    additional_data = models.TextField(blank=True, default='{}')  # Any additional context as JSON string

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action_type', 'timestamp']),
            models.Index(fields=['object_type', 'object_id']),
        ]

    def __str__(self):
        user_str = self.user.username if self.user else 'System'
        return f"{user_str} - {self.action_type} - {self.object_type} - {self.timestamp}"

    def get_changes(self):
        """Get changes as a Python dict"""
        import json
        try:
            return json.loads(self.changes) if self.changes else {}
        except json.JSONDecodeError:
            return {}

    def set_changes(self, changes_dict):
        """Set changes from a Python dict"""
        import json
        self.changes = json.dumps(changes_dict) if changes_dict else '{}'

    def get_additional_data(self):
        """Get additional data as a Python dict"""
        import json
        try:
            return json.loads(self.additional_data) if self.additional_data else {}
        except json.JSONDecodeError:
            return {}

    def set_additional_data(self, data_dict):
        """Set additional data from a Python dict"""
        import json
        self.additional_data = json.dumps(data_dict) if data_dict else '{}'


# Signal to create UserProfile when User is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)
