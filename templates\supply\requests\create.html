{% extends 'base_new.html' %}

{% block title %}Create Supply Request - MSRRMS{% endblock %}

{% block content %}
<h3 class="text-gray-700 text-3xl font-medium">Create Supply Request</h3>

<div class="mt-8">
    <div class="bg-white shadow-md rounded-lg p-6" x-data="requestForm()">
        <form method="post"
              hx-post="{% url 'supply:request_create' %}"
              hx-target="#form-container"
              hx-swap="outerHTML"
              hx-indicator="#form-loading"
              @submit="handleSubmit">
            <div id="form-container">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 gap-6 mt-4 sm:grid-cols-2">
                    <div>
                        <label for="{{ form.item.id_for_label }}" class="text-gray-700">Supply Item <span class="text-red-500">*</span></label>
                        {{ form.item }}
                        {% if form.item.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.item.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.quantity.id_for_label }}" class="text-gray-700">Quantity <span class="text-red-500">*</span></label>
                        {{ form.quantity }}
                        {% if form.quantity.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.quantity.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div class="mt-4">
                    <div id="item-info">
                        <!-- Will be populated by HTMX -->
                    </div>
                </div>

                <div class="mt-4">
                    <label for="{{ form.purpose.id_for_label }}" class="text-gray-700">Purpose <span class="text-red-500">*</span></label>
                    {{ form.purpose }}
                    <p class="mt-1 text-sm text-gray-500">Describe why you need these supplies (minimum 10 characters).</p>
                    {% if form.purpose.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.purpose.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                {% if user.userprofile.department %}
                <div class="mt-6 bg-gray-50 p-4 rounded-md border border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700">Request Information</h3>
                    <div class="mt-2 text-sm text-gray-600">
                        <p><strong>Department:</strong> {{ user.userprofile.department }}</p>
                        <p><strong>Requested by:</strong> {{ user.get_full_name|default:user.username }}</p>
                    </div>
                </div>
                {% endif %}

                <div class="flex justify-end mt-6 space-x-3">
                    <a href="{% url 'supply:request_history' %}"
                       class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:bg-gray-300">
                        Cancel
                    </a>
                    <button type="submit"
                            :disabled="isSubmitting"
                            :class="isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'"
                            class="px-4 py-2 text-white bg-blue-600 rounded-md focus:outline-none focus:bg-blue-700 transition-all">
                        <svg x-show="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span x-text="isSubmitting ? 'Submitting...' : 'Submit Request'"></span>
                    </button>
                    <div id="form-loading" class="htmx-indicator flex items-center text-blue-600">
                        <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function requestForm() {
    return {
        selectedItem: '',
        isSubmitting: false,

        updateItemInfo() {
            // This function is now simpler as HTMX handles showing/hiding content
            this.selectedItem = document.getElementById('{{ form.item.id_for_label }}').value;
        },

        handleSubmit() {
            this.isSubmitting = true;
            // The notification can be triggered via HTMX events for better consistency
        },

        validateQuantity(event) {
            const value = parseInt(event.target.value);
            const selectedOption = document.querySelector(`#{{ form.item.id_for_label }} option:checked`);

            if (selectedOption && selectedOption.value) {
                const availableStock = parseInt(selectedOption.textContent.match(/\((\d+)/)?.[1] || 0);
                if (value > availableStock) {
                    showNotification('warning', 'Quantity Warning', `Requested quantity (${value}) exceeds available stock (${availableStock})`);
                }
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const itemSelect = document.getElementById('{{ form.item.id_for_label }}');
    
    if (itemSelect) {
        itemSelect.setAttribute('hx-get', '{% url "supply:item_info" %}');
        itemSelect.setAttribute('hx-target', '#item-info');
        itemSelect.setAttribute('hx-trigger', 'change');
        htmx.process(itemSelect);
    }

    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        const form = evt.target.closest('form');
        if (form) {
            showNotification('info', 'Processing', 'Submitting your request...');
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful) {
            const form = event.target.closest('form');
            if (form && event.detail.target.id === 'form-container') {
                showNotification('success', 'Request Submitted', 'Your supply request has been submitted successfully.');
            }
        } else if (event.detail.failed) {
            showNotification('error', 'Submission Error', 'There was an error submitting your request. Please check the form and try again.');
        }
    });
});
</script>
{% endblock %}