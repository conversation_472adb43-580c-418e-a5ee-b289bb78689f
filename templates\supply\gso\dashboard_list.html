<!-- Request List Table -->
<div class="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
    {% if page_obj.object_list %}
    <div class="px-6 py-5">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-xl font-semibold text-gray-900">
                    Supply Requests
                </h3>
                <p class="text-sm text-gray-500 mt-1">
                    {{ page_obj.paginator.count }} total request{{ page_obj.paginator.count|pluralize }}
                </p>
            </div>

            <!-- Bulk Actions -->
            <div class="flex items-center space-x-4">
                <!-- Select All Checkbox -->
                <div class="flex items-center">
                    <input type="checkbox"
                           id="select-all"
                           @change="toggleAll($event.target.checked)"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200">
                    <label for="select-all" class="ml-2 text-sm font-medium text-gray-700">Select All</label>
                </div>

                <!-- Bulk Actions Button -->
                <div x-show="selectedRequests.length > 0" x-transition class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600" x-text="selectedRequests.length + ' selected'"></span>
                    <button @click="showBulkActions = !showBulkActions"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        Bulk Actions
                        <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto rounded-lg border border-gray-200">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                    <tr>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            <span class="sr-only">Select</span>
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Request ID
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Department
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Requester
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Item Details
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Quantity
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for request in page_obj.object_list %}
                    <tr class="hover:bg-gray-50" 
                        :class="{ 'bg-blue-50': isSelected({{ request.id }}) }">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" 
                                   name="request_checkbox"
                                   value="{{ request.id }}"
                                   @change="toggleRequest({{ request.id }})"
                                   :checked="isSelected({{ request.id }})"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            <a href="{% url 'supply:gso_request_detail' request.id %}" 
                               class="text-blue-600 hover:text-blue-900 font-semibold">
                                {{ request.request_id }}
                            </a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                        <span class="text-xs font-medium text-gray-600">
                                            {{ request.department|slice:":2"|upper }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm font-medium text-gray-900">{{ request.department }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>
                                <div class="font-medium">{{ request.requester.get_full_name|default:request.requester.username }}</div>
                                <div class="text-gray-500">{{ request.requester.email }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>
                                <div class="font-medium">{{ request.item.name }}</div>
                                <div class="text-gray-500">{{ request.item.description|truncatechars:30 }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div class="flex items-center">
                                <span class="font-semibold">{{ request.quantity }}</span>
                                <span class="ml-1 text-gray-500">{{ request.item.unit }}</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                Available: {{ request.item.current_stock }}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if request.status == 'PENDING' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Pending
                                </span>
                            {% elif request.status == 'APPROVED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-blue-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Approved
                                </span>
                            {% elif request.status == 'REJECTED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Rejected
                                </span>
                            {% elif request.status == 'RELEASED' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                        <circle cx="4" cy="4" r="3" />
                                    </svg>
                                    Released
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div>{{ request.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs">{{ request.created_at|time:"g:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{% url 'supply:gso_request_detail' request.id %}" 
                                   class="text-blue-600 hover:text-blue-900 text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded">
                                    View
                                </a>
                                {% if request.status == 'PENDING' %}
                                    {% if request.item.current_stock >= request.quantity %}
                                        <form hx-post="{% url 'supply:approve_request' request.id %}"
                                              hx-target="#request-list"
                                              hx-confirm="Are you sure you want to approve this request?"
                                              style="display: inline;">
                                            {% csrf_token %}
                                            <button type="submit"
                                                    class="text-green-600 hover:text-green-900 text-xs bg-green-100 hover:bg-green-200 px-2 py-1 rounded">
                                                Approve
                                            </button>
                                        </form>
                                    {% else %}
                                        <span class="text-gray-400 text-xs bg-gray-100 px-2 py-1 rounded cursor-not-allowed"
                                              title="Insufficient stock">
                                            Approve
                                        </span>
                                    {% endif %}
                                    <form hx-post="{% url 'supply:reject_request' request.id %}"
                                          hx-target="#request-list"
                                          hx-confirm="Are you sure you want to reject this request?"
                                          style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900 text-xs bg-red-100 hover:bg-red-200 px-2 py-1 rounded">
                                            Reject
                                        </button>
                                    </form>
                                {% elif request.status == 'APPROVED' %}
                                    <form hx-post="{% url 'supply:release_request' request.id %}"
                                          hx-target="#request-list"
                                          hx-confirm="Are you sure you want to release this request?"
                                          style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit"
                                                class="text-purple-600 hover:text-purple-900 text-xs bg-purple-100 hover:bg-purple-200 px-2 py-1 rounded">
                                            Release
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       hx-get="{% url 'supply:gso_dashboard' %}?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       hx-target="#request-list"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       hx-get="{% url 'supply:gso_dashboard' %}?page={{ page_obj.next_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       hx-target="#request-list"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium">{{ page_obj.paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               hx-get="{% url 'supply:gso_dashboard' %}?page={{ page_obj.previous_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               hx-target="#request-list"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   hx-get="{% url 'supply:gso_dashboard' %}?page={{ num }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   hx-target="#request-list"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               hx-get="{% url 'supply:gso_dashboard' %}?page={{ page_obj.next_page_number }}&status={{ status_filter }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               hx-target="#request-list"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No requests found</h3>
        <p class="mt-1 text-sm text-gray-500">
            {% if status_filter or department_filter or search_query %}
                Try adjusting your filters to see more results.
            {% else %}
                No supply requests have been submitted yet.
            {% endif %}
        </p>
        {% if status_filter or department_filter or search_query %}
        <div class="mt-6">
            <a href="{% url 'supply:gso_dashboard' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Clear Filters
            </a>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- CSRF Token for HTMX requests -->
{% csrf_token %}